<div class="container-fluid">
  <!-- Loading Global -->
  <div *ngIf="carregandoDados" class="d-flex flex-column align-items-center justify-content-center" style="min-height: 400px;">
    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
      <span class="sr-only">Carregando...</span>
    </div>
    <h5 class="text-muted">Carregando dados do contato...</h5>
    <p class="text-muted small">Por favor, aguarde</p>
  </div>

  <!-- <PERSON><PERSON><PERSON><PERSON> (mostrar apenas quando não está carregando) -->
  <div *ngIf="!carregandoDados">
    <div class="row">
      <div class="col-12">
        <!-- Header -->
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="javascript: void(0);">CRM</a></li>
              <li class="breadcrumb-item active">Assistente WhatsApp</li>
            </ol>
          </div>
          <h4 class="page-title">
            <i class="fe-message-square"></i> Assistente de Vendas - WhatsApp
          </h4>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
      
      <!-- Card de Status do Lead (Novo) -->
      <div class="card mb-3" [ngClass]="{'border-warning': !contato.id, 'border-success': contato.id}" 
           style="border-width: 2px;">
        <div class="card-body py-3">
          <div class="d-flex align-items-center justify-content-between">
            <div>
              <h6 class="mb-0">
                <i [class]="contato.id ? 'fe-check-circle text-success' : 'fe-alert-circle text-warning'"></i>
                {{ contato.id ? 'Lead Cadastrado' : 'Novo Lead - Não Cadastrado' }}
              </h6>
              <p class="text-muted mb-0 small">
                {{ contato.id ? 'Este contato já está no seu CRM' : 'Oportunidade de novo negócio!' }}
              </p>
            </div>
            <button *ngIf="!contato.id" class="btn btn-primary btn-sm" 
                    data-bs-toggle="collapse" 
                    data-bs-target="#capturaRapida">
              <i class="fe-user-plus"></i> Cadastrar Lead
            </button>
          </div>
        </div>
      </div>
      
      <!-- Card de Informações do Contato -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-user"></i> Informações do Contato
            <span *ngIf="!contato.id" class="badge badge-warning ms-2">NOVO</span>
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-8">
              <div>
                <div>
                  <!-- Nome da Empresa (Destaque Principal) -->
                  <h3 class="mb-1 text-dark">
                    <i class="fe-briefcase text-primary"></i>
                    {{ contato.empresa || 'Empresa não informada' }}
                    <!-- Badge de Sistema Concorrente -->
                    <span *ngIf="contato.sistemaConcorrente" 
                          class="badge badge-danger ms-2"
                          data-bs-toggle="tooltip"
                          [title]="'Usa: ' + contato.sistemaConcorrente">
                      <i class="fe-alert-octagon"></i> Usa Concorrente
                    </span>
                  </h3>
                  
                  <!-- Nome do Contato (Secundário) -->
                  <h6 class="text-muted mb-2">
                    <!-- Se tem lead cadastrado, mostra o nome do banco -->
                    <span *ngIf="contato.id">
                      <i class="fe-user"></i> {{ contato.nome }}
                    </span>
                    
                    <!-- Se não tem lead cadastrado, mostra nome do WhatsApp ou "Contato não cadastrado" -->
                    <span *ngIf="!contato.id">
                      <i class="fe-user"></i> {{ nomeWhatsApp || 'Contato não cadastrado' }}
                      <span class="badge badge-warning ms-2">Não Cadastrado</span>
                    </span>
                  </h6>
                  
                  <!-- Telefone -->
                  <p class="text-muted mb-0">
                    <i class="fe-phone"></i> {{ contato.telefone }}
                  </p>
                  
                  <small *ngIf="!contato.id && nomeWhatsApp" class="text-muted">
                    <i class="fe-alert-circle text-warning"></i> 
                    Este contato ainda não está no seu CRM
                  </small>
                </div>
              </div>
              
              <!-- Informações Adicionais (Bio Instagram) -->
              <div class="mt-3 pt-3 border-top" *ngIf="contato.bioInsta">
                <!-- Bio do Instagram -->
                <div>
                  <strong class="text-info">
                    <i class="fe-instagram"></i> Bio Instagram:
                  </strong>
                  <p class="mb-0 text-muted small mt-1">{{ contato.bioInsta }}</p>
                </div>
              </div>
            </div>
            
            <div class="col-md-4">
              <div class="text-end">
                <span class="badge badge-success mb-2">
                  <i class="fe-clock"></i> Online agora
                </span>
                
                <!-- Instagram Handle -->
                <div *ngIf="contato.instagramHandle" class="mt-2">
                  <a [href]="'https://instagram.com/' + contato.instagramHandle" 
                     target="_blank" 
                     class="text-decoration-none">
                    <span class="badge badge-info">
                      <i class="fe-instagram"></i> @{{ contato.instagramHandle }}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Card de Inteligência Competitiva -->
      <div class="card border-danger mb-3" *ngIf="contato.sistemaConcorrente">
        <div class="card-header bg-danger text-white">
          <h5 class="card-title mb-0">
            <i class="fe-shield-off"></i> Inteligência Competitiva
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-danger mb-2">
                <i class="fe-monitor"></i> Sistema Atual: <strong>{{ contato.sistemaConcorrente }}</strong>
              </h6>
              
              <!-- Informações do Concorrente -->
              <div class="mt-3">
                <small class="text-muted d-block mb-2">
                  <i class="fe-info"></i> Pontos de atenção:
                </small>
                <ul class="list-unstyled ms-3">
                  <li *ngFor="let ponto of getConcorrenteInfo(contato.sistemaConcorrente).pontosFracos"
                      class="mb-1">
                    <small><i class="fe-minus text-danger"></i> {{ ponto }}</small>
                  </li>
                </ul>
              </div>
            </div>
            
            <div class="col-md-6">
              <h6 class="text-success mb-2">
                <i class="fe-trending-up"></i> Nossos Diferenciais
              </h6>
              
              <ul class="list-unstyled ms-3">
                <li *ngFor="let diferencial of getConcorrenteInfo(contato.sistemaConcorrente).diferenciais"
                    class="mb-1">
                  <small><i class="fe-check text-success"></i> {{ diferencial }}</small>
                </li>
              </ul>
              
              <!-- Sugestão de Abordagem -->
              <div class="alert alert-warning mt-3 mb-0" *ngIf="getConcorrenteInfo(contato.sistemaConcorrente).sugestaoAbordagem">
                <small>
                  <i class="fe-message-circle"></i> 
                  <strong>Dica:</strong> {{ getConcorrenteInfo(contato.sistemaConcorrente).sugestaoAbordagem }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Captura Rápida (só aparece para não cadastrados) -->
      <div class="collapse" id="capturaRapida" *ngIf="!contato.id">
        <div class="card bg-light mb-3">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fe-edit-3"></i> Captura Rápida de Informações
            </h6>
          </div>
          <div class="card-body">
            <div class="row g-2">
              <div class="col-md-6">
                <label class="form-label small">Nome do contato</label>
                <input type="text" class="form-control" placeholder="Nome do responsável" 
                       [value]="nomeWhatsApp" readonly
                       style="background-color: #f8f9fa;">
              </div>
              <div class="col-md-6">
                <label class="form-label small">Nome da empresa</label>
                <input type="text" class="form-control" placeholder="Ex: Restaurante do João" 
                       [(ngModel)]="novoLead.empresa">
              </div>
              <div class="col-md-6">
                <label class="form-label small">Email</label>
                <input type="email" class="form-control" placeholder="<EMAIL>" 
                       [(ngModel)]="novoLead.email">
              </div>
              <div class="col-md-6">
                <label class="form-label small">Telefone</label>
                <input type="text" class="form-control" [value]="contato.telefone" readonly
                       style="background-color: #f8f9fa;">
              </div>
              <div class="col-12">
                <label class="form-label small">Observações rápidas</label>
                <textarea class="form-control" rows="4" 
                          placeholder="Ex: Interessado em cardápio digital, tem 2 lojas..."
                          [(ngModel)]="novoLead.observacoes"></textarea>
              </div>
              <div class="col-12 text-end mt-3">
                <button class="btn btn-secondary btn-sm me-2" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#capturaRapida">
                  <i class="fe-x"></i> Cancelar
                </button>
                <button class="btn btn-success" (click)="cadastrarLeadRapido()">
                  <i class="fe-save"></i> Salvar e Cadastrar Lead
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Assistente de Resposta (Unificado) -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-zap"></i> Assistente de Vendas SPIN
          </h5>
        </div>
        <div class="card-body">
          <!-- Seção 1: Configuração da Sugestão -->
          <div class="config-section mb-4 p-3 bg-light rounded">
            <!-- Toggle Modo Rapport e Apresentação -->
            <div class="mb-3">
              <h6 class="mb-2">
                <i class="fe-target text-primary"></i> Modo de Geração
              </h6>
              <div class="d-flex gap-3">
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="modoRapportSwitch"
                         [(ngModel)]="modoRapport"
                         (change)="toggleModoRapport()">
                  <label class="form-check-label" for="modoRapportSwitch">
                    <strong [class.text-primary]="modoRapport">
                      <i class="fe-heart"></i> Rapport/Atratividade
                    </strong>
                  </label>
                </div>
                <div class="form-check form-switch">
                  <input class="form-check-input"
                         type="checkbox"
                         id="modoApresentacaoSwitch"
                         [(ngModel)]="modoApresentacao"
                         (change)="toggleModoApresentacao()">
                  <label class="form-check-label" for="modoApresentacaoSwitch">
                    <strong [class.text-success]="modoApresentacao">
                      <i class="fe-user-check"></i> Apresentação Profissional
                    </strong>
                  </label>
                </div>
              </div>
            </div>

            <!-- Configurações Modo Normal (SPIN) -->
            <div *ngIf="!modoRapport && !modoApresentacao" class="row align-items-center">
              <!-- Fase SPIN -->
              <div class="col-md-8">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-target text-primary"></i> Fase da Venda (SPIN Selling):
                </label>
                <div class="btn-group d-flex flex-wrap" role="group">
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('auto')"
                          (click)="mudarFaseSpin('auto')"
                          data-bs-toggle="tooltip"
                          title="Deixar a IA detectar automaticamente a fase">
                    <i class="fe-cpu"></i> Auto-detectar
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('rapport')"
                          (click)="mudarFaseSpin('rapport')"
                          data-bs-toggle="tooltip"
                          title="Lead não respondeu ainda">
                    <i class="fe-heart"></i> Rapport
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('situacao')"
                          (click)="mudarFaseSpin('situacao')"
                          data-bs-toggle="tooltip"
                          title="Entender o contexto atual do cliente">
                    <i class="fe-help-circle"></i> Situação
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('problema')"
                          (click)="mudarFaseSpin('problema')"
                          data-bs-toggle="tooltip"
                          title="Identificar dores e dificuldades">
                    <i class="fe-alert-triangle"></i> Problema
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="getFaseBadgeClass('implicacao')"
                          (click)="mudarFaseSpin('implicacao')"
                          data-bs-toggle="tooltip"
                          title="Explorar impactos dos problemas">
                    <i class="fe-trending-down"></i> Implicação
                  </button>
                  <button type="button"
                          class="btn flex-fill mb-1"
                          [ngClass]="getFaseBadgeClass('necessidade')"
                          (click)="mudarFaseSpin('necessidade')"
                          data-bs-toggle="tooltip"
                          title="Demonstrar valor da solução">
                    <i class="fe-check-circle"></i> Necessidade
                  </button>
                </div>
                <small class="text-muted d-block mt-1">
                  <i class="fe-info"></i> Selecione a fase manualmente ou deixe a IA detectar automaticamente
                  <span *ngIf="faseDetectadaAutomaticamente" class="text-success">
                    - IA detectou: <strong>{{ faseDetectadaAutomaticamente }}</strong>
                  </span>
                </small>
              </div>

              <!-- Tom da Conversa -->
              <div class="col-md-4">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-mic text-primary"></i> Tom:
                </label>
                <select class="form-select" [(ngModel)]="tipoTom">
                  <option value="formal">Formal</option>
                  <option value="informal">Informal</option>
                  <option value="tecnico">Técnico</option>
                </select>
              </div>
            </div>

            <!-- Configurações Modo Rapport -->
            <div *ngIf="modoRapport" class="row align-items-center">
              <div class="col-12">
                <label class="form-label fw-bold mb-2">
                  <i class="fe-message-circle text-primary"></i> Tipo de Abordagem Outbound:
                </label>
                <div class="btn-group d-flex flex-wrap" role="group">
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="tipoAbordagem === 'direta' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('direta')"
                          data-bs-toggle="tooltip"
                          title="Abordagem direta e objetiva, focada em resultados">
                    <i class="fe-arrow-right"></i> Direta
                  </button>
                  <button type="button"
                          class="btn flex-fill me-1 mb-1"
                          [ngClass]="tipoAbordagem === 'indireta' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('indireta')"
                          data-bs-toggle="tooltip"
                          title="Abordagem mais suave, criando conexão primeiro">
                    <i class="fe-smile"></i> Indireta
                  </button>
                  <button type="button"
                          class="btn flex-fill mb-1"
                          [ngClass]="tipoAbordagem === 'consultiva' ? 'btn btn-primary' : 'btn btn-outline-primary'"
                          (click)="mudarTipoAbordagem('consultiva')"
                          data-bs-toggle="tooltip"
                          title="Abordagem consultiva, focada em entender necessidades">
                    <i class="fe-users"></i> Consultiva
                  </button>
                </div>
                <small class="text-muted d-block mt-1">
                  <i class="fe-info"></i> Escolha o estilo de abordagem para gerar mensagens que despertem interesse e engajamento
                </small>
              </div>
            </div>
          </div>

          <!-- Seção 2: Botão de Gerar -->
          <div class="text-center mb-4">
            <button class="btn btn-primary btn-lg shadow"
                    [disabled]="carregando"
                    (click)="gerarSugestao()">
              <span *ngIf="!carregando && !modoRapport && !modoApresentacao">
                <i class="fe-cpu"></i> Gerar Sugestão de Resposta
              </span>
              <span *ngIf="!carregando && modoRapport">
                <i class="fe-heart"></i> Gerar Mensagem de Rapport
              </span>
              <span *ngIf="!carregando && modoApresentacao">
                <i class="fe-user-check"></i> Gerar Apresentação Profissional
              </span>
              <span *ngIf="carregando && !modoRapport && !modoApresentacao">
                <i class="fe-loader rotating"></i> Analisando conversa...
              </span>
              <span *ngIf="carregando && modoRapport">
                <i class="fe-loader rotating"></i> Criando mensagem atrativa...
              </span>
              <span *ngIf="carregando && modoApresentacao">
                <i class="fe-loader rotating"></i> Criando apresentação profissional...
              </span>
            </button>
            <div *ngIf="modoRapport" class="mt-2">
              <small class="text-info">
                <i class="fe-info"></i> Mensagens otimizadas para trabalho outbound e engajamento inicial
              </small>
            </div>
            <div *ngIf="modoApresentacao" class="mt-2">
              <small class="text-success">
                <i class="fe-info"></i> Mensagem de apresentação profissional para primeiro contato
              </small>
            </div>
          </div>

          <!-- Erro -->
          <div *ngIf="erro" class="alert alert-danger" role="alert">
            <i class="fe-alert-triangle"></i>
            {{ erro }}
          </div>

          <!-- Sugestões geradas -->
          <div *ngIf="sugestoes.length > 0 && !carregando" class="sugestoes-container">
            <label class="form-label mb-3">
              <i class="fe-edit-3"></i> Sugestões geradas:
              <span class="badge badge-primary ms-2">{{ sugestoes.length }} opções</span>
            </label>

            <!-- Lista vertical de sugestões -->
            <div class="sugestoes-lista">
              <div *ngFor="let sug of sugestoes; let i = index" class="sugestao-item mb-3">
                <div class="card">
                  <div class="card-header py-2">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="mb-0">
                        <i class="fe-message-square"></i> Sugestão {{ i + 1 }}
                        <span class="badge badge-secondary ms-2" *ngIf="sug.faseSpin">
                          Fase: {{ sug.faseSpin | uppercase }}
                        </span>
                      </h6>
                      <span class="badge badge-info">{{ (sug.confianca * 100) | number:'1.0-0' }}% confiança</span>
                    </div>
                  </div>
                  <div class="card-body">
                    <textarea class="form-control mb-3"
                              rows="5"
                              [(ngModel)]="sug.texto"
                              placeholder="Sugestão..."></textarea>
                    
                    <!-- Ações individuais para cada sugestão -->
                    <div class="d-flex gap-2">
                      <button class="btn btn-sm btn-success"
                              (click)="copiarSugestao(i)">
                        <i class="fe-copy"></i> Copiar
                      </button>
                      <button class="btn btn-sm btn-outline-primary"
                              (click)="editarSugestaoIndividual(i)">
                        <i class="fe-edit"></i> Editar
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Botão para regenerar todas as sugestões -->
            <div class="text-center mt-3">
              <button class="btn btn-outline-secondary"
                      (click)="regenerarSugestao()"
                      [disabled]="carregando">
                <i class="fe-refresh-cw"></i> Regenerar Todas
              </button>
            </div>
            
            <!-- Observações sobre a fase detectada -->
            <div *ngIf="faseDetectadaAutomaticamente" class="alert alert-info mt-3">
              <i class="fe-info"></i> {{ faseDetectadaAutomaticamente }}
            </div>
          </div>
        </div>
      </div>

      <!-- Card de Contexto da Conversa -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fe-message-circle"></i> Contexto da Conversa
          </h5>
        </div>
        <div class="card-body">
          <!-- Resumo da conversa -->
          <div class="mb-3">
            <label class="form-label">
              <i class="fe-file-text"></i> Resumo:
            </label>
            <p class="text-muted">{{ contextoResumo }}</p>
          </div>

          <!-- Mensagens capturadas -->
          <div class="mb-3">
            <label class="form-label">
              <i class="fe-message-square"></i> Mensagens capturadas:
              <span class="badge badge-secondary ms-2">{{ mensagensCapturadas.length }}</span>
            </label>
            
            <div *ngIf="capturandoMensagens" class="alert alert-info">
              <i class="fe-loader rotating"></i> Aguardando mensagens do WhatsApp...
            </div>

            <div *ngIf="!capturandoMensagens && mensagensCapturadas.length === 0" 
                 class="alert alert-warning">
              <i class="fe-alert-circle"></i> Nenhuma mensagem capturada ainda
            </div>

            <div *ngIf="mensagensCapturadas.length > 0" 
                 class="messages-container p-3 bg-light rounded"
                 style="max-height: 300px; overflow-y: auto;">
              <div *ngFor="let msg of mensagensCapturadas" 
                   class="message-item mb-2 p-2 rounded"
                   [ngClass]="{'bg-white': msg.remetente === 'Lead', 
                              'bg-primary text-white': msg.remetente === 'Eu'}">
                <small class="d-block">
                  <strong>{{ msg.remetente }}</strong> - {{ msg.horario }}
                </small>
                <p class="mb-0">{{ msg.texto }}</p>
              </div>
            </div>
          </div>

          <!-- Botões de ação -->
          <div class="d-flex gap-2">
            <button class="btn btn-outline-primary btn-sm" 
                    (click)="simularMensagens()"
                    [disabled]="!capturandoMensagens">
              <i class="fe-play"></i> Simular Mensagens
            </button>
            <button class="btn btn-outline-secondary btn-sm" 
                    (click)="limparMensagens()"
                    [disabled]="mensagensCapturadas.length === 0">
              <i class="fe-trash-2"></i> Limpar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</div>
