<!-- WhatsApp Assistant 3.0 - Design Moderno -->
<div class="container-fluid">
  
  <!-- Loading -->
  <div *ngIf="carregandoDados" class="d-flex flex-column align-items-center justify-content-center" style="min-height: 400px;">
    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
      <span class="sr-only">Carregando...</span>
    </div>
    <h5 class="text-muted">Carregando dados do contato...</h5>
  </div>

  <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
  <div *ngIf="!carregandoDados">
    
    <!-- Header da <PERSON>a -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="javascript: void(0);">CRM</a></li>
              <li class="breadcrumb-item active">Assistente WhatsApp</li>
            </ol>
          </div>
          <h4 class="page-title">
            <i class="fe-message-square"></i> Assistente de Vendas - WhatsApp
          </h4>
        </div>
      </div>
    </div>

    <!-- Card 1: Informações do Lead -->
    <div class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-primary text-white py-3">
        <div class="row align-items-center">
          <div class="col-8">
            <div class="d-flex align-items-center">
              <div class="avatar-circle bg-white text-primary me-3">
                <i class="fe-user fs-4"></i>
              </div>
              <div>
                <h5 class="mb-1 fw-bold">{{ contato.nome || nomeWhatsApp || 'Novo Contato' }}</h5>
                <p class="mb-0 opacity-75">
                  <i class="fe-briefcase me-1"></i>
                  {{ contato.empresa || 'Empresa não informada' }}
                </p>
                <small class="opacity-75">
                  <i class="fe-phone me-1"></i>
                  {{ contato.telefone }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-4 text-end">
            <div class="mb-2">
              <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                <i class="fe-star me-1"></i>
                85% Score
              </span>
            </div>
            <div class="small">
              <i class="fe-circle text-success me-1"></i>
              Online agora
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-body bg-light">
        <div class="row g-3">
          <!-- Status Cards -->
          <div class="col-md-3">
            <div class="status-card bg-primary text-white text-center p-3 rounded">
              <i class="fe-target fs-2 mb-2"></i>
              <h6 class="mb-1">Fase Atual</h6>
              <strong>{{ getFaseDisplayName(faseSpin) }}</strong>
            </div>
          </div>
          <div class="col-md-3">
            <div class="status-card bg-success text-white text-center p-3 rounded">
              <i class="fe-clock fs-2 mb-2"></i>
              <h6 class="mb-1">Resposta</h6>
              <strong>2 minutos</strong>
            </div>
          </div>
          <div class="col-md-3">
            <div class="status-card bg-info text-white text-center p-3 rounded">
              <i class="fe-trending-up fs-2 mb-2"></i>
              <h6 class="mb-1">Potencial</h6>
              <strong>Alto Valor</strong>
            </div>
          </div>
          <div class="col-md-3">
            <div class="status-card bg-warning text-dark text-center p-3 rounded">
              <i class="fe-zap fs-2 mb-2"></i>
              <h6 class="mb-1">Prioridade</h6>
              <strong>Hot Lead</strong>
            </div>
          </div>
        </div>
        
        <!-- Alerta Concorrente -->
        <div *ngIf="contato.sistemaConcorrente" class="alert alert-danger mt-3 mb-0">
          <div class="row align-items-center">
            <div class="col-8">
              <h6 class="alert-heading mb-1">
                <i class="fe-alert-triangle me-2"></i>
                Usa Concorrente: {{ contato.sistemaConcorrente }}
              </h6>
              <p class="mb-0">Taxa atual: 27% sobre vendas</p>
            </div>
            <div class="col-4 text-end">
              <div class="badge bg-success fs-6 px-3 py-2">
                <i class="fe-dollar-sign me-1"></i>
                Economia: R$ 1.500/mês
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 2: Assistente IA -->
    <div class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-success text-white py-3">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <i class="fe-cpu me-2 fs-5"></i>
            <div>
              <h5 class="mb-0 fw-bold">Assistente IA</h5>
              <small class="opacity-75">Fase: {{ getFaseDisplayName(faseSpin) }} → {{ getNextPhase(faseSpin) || 'Fechamento' }}</small>
            </div>
          </div>
          <div class="badge bg-white text-success px-3 py-2">
            <i class="fe-zap me-1"></i>
            IA Ativa
          </div>
        </div>
      </div>
      <div class="card-body">
        
        <!-- Configuração de Modo -->
        <div class="row mb-4">
          <div class="col-12">
            <h6 class="text-muted mb-3">
              <i class="fe-settings me-1"></i>
              Configuração do Assistente:
            </h6>
            <div class="form-check form-switch d-inline-block me-4">
              <input class="form-check-input" type="checkbox" id="modoRapport" 
                     [(ngModel)]="modoRapport" (change)="toggleModoRapport()">
              <label class="form-check-label fw-bold" for="modoRapport">
                <i class="fe-heart text-danger me-1"></i>
                Rapport/Atratividade
              </label>
            </div>
            <div class="form-check form-switch d-inline-block">
              <input class="form-check-input" type="checkbox" id="modoApresentacao" 
                     [(ngModel)]="modoApresentacao" (change)="toggleModoApresentacao()">
              <label class="form-check-label fw-bold" for="modoApresentacao">
                <i class="fe-user-check text-info me-1"></i>
                Apresentação Profissional
              </label>
            </div>
          </div>
        </div>
        
        <!-- Configuração SPIN (modo normal) -->
        <div *ngIf="!modoRapport && !modoApresentacao" class="mb-4">
          <h6 class="text-primary mb-3">
            <i class="fe-target me-1"></i>
            Fase da Venda (SPIN Selling):
          </h6>
          <div class="btn-group d-flex flex-wrap" role="group">
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('auto')"
                    (click)="mudarFaseSpin('auto')">
              <i class="fe-cpu"></i><br>
              <small>Auto-detectar</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('situacao')"
                    (click)="mudarFaseSpin('situacao')">
              <i class="fe-help-circle"></i><br>
              <small>Situação</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('problema')"
                    (click)="mudarFaseSpin('problema')">
              <i class="fe-alert-triangle"></i><br>
              <small>Problema</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('implicacao')"
                    (click)="mudarFaseSpin('implicacao')">
              <i class="fe-trending-down"></i><br>
              <small>Implicação</small>
            </button>
            <button type="button" class="btn flex-fill mb-1"
                    [ngClass]="getFaseBadgeClass('necessidade')"
                    (click)="mudarFaseSpin('necessidade')">
              <i class="fe-check-circle"></i><br>
              <small>Necessidade</small>
            </button>
          </div>
          
          <div class="row mt-3">
            <div class="col-md-4">
              <label class="form-label fw-bold">Tom da Conversa:</label>
              <select class="form-select" [(ngModel)]="tipoTom">
                <option value="formal">Formal</option>
                <option value="informal">Informal</option>
                <option value="tecnico">Técnico</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Configuração Rapport (modo rapport) -->
        <div *ngIf="modoRapport" class="mb-4">
          <h6 class="text-danger mb-3">
            <i class="fe-heart me-1"></i>
            Tipo de Abordagem Outbound:
          </h6>
          <div class="btn-group d-flex" role="group">
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'direta' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('direta')">
              <i class="fe-arrow-right"></i> Direta
            </button>
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'indireta' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('indireta')">
              <i class="fe-smile"></i> Indireta
            </button>
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'consultiva' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('consultiva')">
              <i class="fe-users"></i> Consultiva
            </button>
          </div>
        </div>
        
        <!-- Botão de Gerar -->
        <div class="text-center mb-4">
          <button class="btn btn-primary btn-lg px-5 py-3 shadow"
                  [disabled]="carregando"
                  (click)="gerarSugestao()">
            <span *ngIf="!carregando && !modoRapport && !modoApresentacao">
              <i class="fe-cpu me-2"></i> Gerar Sugestão de Resposta
            </span>
            <span *ngIf="!carregando && modoRapport">
              <i class="fe-heart me-2"></i> Gerar Mensagem de Rapport
            </span>
            <span *ngIf="!carregando && modoApresentacao">
              <i class="fe-user-check me-2"></i> Gerar Apresentação Profissional
            </span>
            <span *ngIf="carregando">
              <i class="fe-loader rotating me-2"></i> {{ getLoadingText() }}
            </span>
          </button>
        </div>
        
        <!-- Informações do Modo -->
        <div *ngIf="modoRapport" class="alert alert-info">
          <i class="fe-info me-2"></i>
          Mensagens otimizadas para trabalho outbound e engajamento inicial
        </div>
        <div *ngIf="modoApresentacao" class="alert alert-success">
          <i class="fe-info me-2"></i>
          Mensagem de apresentação profissional para primeiro contato
        </div>
      </div>
    </div>

    <!-- Card 3: Sugestões Geradas -->
    <div *ngIf="sugestoes.length > 0 && !carregando" class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-warning text-dark py-3">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <i class="fe-edit-3 me-2 fs-5"></i>
            <div>
              <h5 class="mb-0 fw-bold">Sugestões Geradas</h5>
              <small>{{ sugestoes.length }} opções disponíveis</small>
            </div>
          </div>
          <div class="badge bg-dark text-white px-3 py-2">
            <i class="fe-zap me-1"></i>
            {{ (sugestoes[sugestaoSelecionada]?.confianca * 100) | number:'1.0-0' }}% confiança
          </div>
        </div>
      </div>
      <div class="card-body">

        <!-- Área de Texto da Sugestão -->
        <div class="mb-4">
          <label class="form-label fw-bold text-primary">
            <i class="fe-message-square me-1"></i>
            Sugestão {{ sugestaoSelecionada + 1 }} de {{ sugestoes.length }}:
          </label>
          <textarea class="form-control form-control-lg"
                    [value]="getSugestaoTexto()"
                    (input)="updateSugestaoTexto($event)"
                    rows="6"
                    placeholder="Sugestão aparecerá aqui..."
                    style="font-size: 16px; line-height: 1.5;"></textarea>
        </div>

        <!-- Botões de Ação -->
        <div class="row g-2 mb-3">
          <div class="col-md-3">
            <button class="btn btn-success w-100" (click)="copiarSugestao(sugestaoSelecionada)">
              <i class="fe-copy me-1"></i>
              Copiar
            </button>
          </div>
          <div class="col-md-3">
            <button class="btn btn-outline-primary w-100" (click)="editarSugestaoIndividual(sugestaoSelecionada)">
              <i class="fe-edit me-1"></i>
              Editar
            </button>
          </div>
          <div class="col-md-3">
            <button class="btn btn-outline-warning w-100" (click)="regenerarSugestao()" [disabled]="carregando">
              <i class="fe-refresh-cw me-1"></i>
              Nova
            </button>
          </div>
          <div class="col-md-3">
            <button class="btn btn-outline-secondary w-100" (click)="enviarSugestao()" *ngIf="false">
              <i class="fe-send me-1"></i>
              Enviar
            </button>
          </div>
        </div>

        <!-- Navegação entre Sugestões -->
        <div *ngIf="sugestoes.length > 1" class="d-flex align-items-center justify-content-center">
          <button class="btn btn-outline-secondary me-2"
                  (click)="selecionarSugestao(sugestaoSelecionada - 1)"
                  [disabled]="sugestaoSelecionada === 0">
            <i class="fe-chevron-left"></i>
          </button>
          <span class="mx-3 fw-bold">
            {{ sugestaoSelecionada + 1 }} de {{ sugestoes.length }}
          </span>
          <button class="btn btn-outline-secondary ms-2"
                  (click)="selecionarSugestao(sugestaoSelecionada + 1)"
                  [disabled]="sugestaoSelecionada === sugestoes.length - 1">
            <i class="fe-chevron-right"></i>
          </button>
        </div>

        <!-- Respostas Rápidas -->
        <div class="mt-4">
          <h6 class="text-muted mb-3">
            <i class="fe-zap me-1"></i>
            Respostas Rápidas:
          </h6>
          <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-outline-primary btn-sm" (click)="gerarRespostaRapida('roi')">
              💰 Falar ROI
            </button>
            <button class="btn btn-outline-success btn-sm" (click)="gerarRespostaRapida('case')">
              📊 Mostrar Case
            </button>
            <button class="btn btn-outline-info btn-sm" (click)="gerarRespostaRapida('demo')">
              📅 Agendar Demo
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Card 4: Erro -->
    <div *ngIf="erro" class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-danger text-white py-3">
        <h5 class="mb-0">
          <i class="fe-alert-triangle me-2"></i>
          Erro
        </h5>
      </div>
      <div class="card-body">
        <p class="mb-0">{{ erro }}</p>
      </div>
    </div>

    <!-- Card 5: Contexto da Conversa -->
    <div class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-info text-white py-3">
        <div class="d-flex align-items-center">
          <i class="fe-message-circle me-2 fs-5"></i>
          <div>
            <h5 class="mb-0 fw-bold">Contexto da Conversa</h5>
            <small class="opacity-75">Análise automática das mensagens</small>
          </div>
        </div>
      </div>
      <div class="card-body">

        <!-- Resumo -->
        <div class="mb-4">
          <h6 class="text-primary mb-2">
            <i class="fe-file-text me-1"></i>
            Resumo da Conversa:
          </h6>
          <p class="text-muted">{{ contextoResumo || 'Aguardando mensagens para análise...' }}</p>
        </div>

        <!-- Mensagens Capturadas -->
        <div class="mb-4">
          <div class="d-flex align-items-center justify-content-between mb-3">
            <h6 class="text-primary mb-0">
              <i class="fe-message-square me-1"></i>
              Mensagens Capturadas:
            </h6>
            <span class="badge bg-secondary">{{ mensagensCapturadas.length }} mensagens</span>
          </div>

          <div *ngIf="capturandoMensagens" class="alert alert-info">
            <i class="fe-loader rotating me-2"></i>
            Aguardando mensagens do WhatsApp...
          </div>

          <div *ngIf="!capturandoMensagens && mensagensCapturadas.length === 0"
               class="alert alert-warning">
            <i class="fe-alert-circle me-2"></i>
            Nenhuma mensagem capturada ainda
          </div>

          <div *ngIf="mensagensCapturadas.length > 0"
               class="messages-container border rounded p-3"
               style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
            <div *ngFor="let msg of mensagensCapturadas"
                 class="message-item mb-2 p-2 rounded"
                 [ngClass]="{'bg-white border-start border-primary border-3': msg.remetente === 'Lead',
                            'bg-primary text-white ms-4': msg.remetente === 'Eu'}">
              <small class="d-block fw-bold">
                {{ msg.remetente }} - {{ msg.horario }}
              </small>
              <p class="mb-0">{{ msg.texto }}</p>
            </div>
          </div>
        </div>

        <!-- Botões de Ação -->
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary"
                  (click)="simularMensagens()"
                  [disabled]="!capturandoMensagens">
            <i class="fe-play me-1"></i>
            Simular Mensagens
          </button>
          <button class="btn btn-outline-secondary"
                  (click)="limparMensagens()"
                  [disabled]="mensagensCapturadas.length === 0">
            <i class="fe-trash-2 me-1"></i>
            Limpar
          </button>
        </div>
      </div>
    </div>

    <!-- Card 6: Captura Rápida (só para novos leads) -->
    <div *ngIf="!contato.id" class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-secondary text-white py-3">
        <h5 class="mb-0">
          <i class="fe-user-plus me-2"></i>
          Cadastrar Novo Lead
        </h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-6">
            <label class="form-label fw-bold">Nome do contato:</label>
            <input type="text" class="form-control"
                   [value]="nomeWhatsApp" readonly
                   style="background-color: #f8f9fa;">
          </div>
          <div class="col-md-6">
            <label class="form-label fw-bold">Nome da empresa:</label>
            <input type="text" class="form-control"
                   placeholder="Ex: Restaurante do João"
                   [(ngModel)]="novoLead.empresa">
          </div>
          <div class="col-md-6">
            <label class="form-label fw-bold">Email:</label>
            <input type="email" class="form-control"
                   placeholder="<EMAIL>"
                   [(ngModel)]="novoLead.email">
          </div>
          <div class="col-md-6">
            <label class="form-label fw-bold">Telefone:</label>
            <input type="text" class="form-control"
                   [value]="contato.telefone" readonly
                   style="background-color: #f8f9fa;">
          </div>
          <div class="col-12">
            <label class="form-label fw-bold">Observações:</label>
            <textarea class="form-control" rows="3"
                      placeholder="Ex: Interessado em cardápio digital, tem 2 lojas..."
                      [(ngModel)]="novoLead.observacoes"></textarea>
          </div>
          <div class="col-12 text-end">
            <button class="btn btn-success btn-lg px-4" (click)="cadastrarLeadRapido()">
              <i class="fe-save me-2"></i>
              Salvar e Cadastrar Lead
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Observações da IA -->
    <div *ngIf="faseDetectadaAutomaticamente" class="alert alert-primary">
      <i class="fe-info me-2"></i>
      {{ faseDetectadaAutomaticamente }}
    </div>

  </div>
</div>
