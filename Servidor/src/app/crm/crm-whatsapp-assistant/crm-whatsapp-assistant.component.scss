// WhatsApp Assistant 2.0 - Painel Lateral
// Estilos otimizados para painel de 350px de largura

// ===== VARIÁVEIS =====
:root {
  --panel-width: 350px;
  --primary-color: #6C5CE7;
  --success-color: #00B894;
  --warning-color: #FDCB6E;
  --danger-color: #E84393;
  --text-color: #2D3436;
  --text-muted: #636E72;
  --bg-light: #FAFBFC;
  --border-color: #E9ECEF;
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-medium: rgba(0, 0, 0, 0.1);
}

// ===== CONTAINER PRINCIPAL =====
.whatsapp-assistant-panel {
  width: var(--panel-width);
  height: 100vh;
  background: var(--bg-light);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  overflow: hidden;
}

// ===== LOADING =====
.loading-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;

  .loading-content {
    text-align: center;

    .spinner-border {
      width: 2rem;
      height: 2rem;
      color: var(--primary-color);
    }

    .loading-text {
      margin: 0;
      color: var(--text-muted);
      font-size: 13px;
    }
  }
}

// ===== CONTEÚDO PRINCIPAL =====
.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  // Scroll customizado
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;

    &:hover {
      background: var(--text-muted);
    }
  }
}

// ===== HEADER COMPACTO =====
.header-section {
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 16px;

  .lead-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .lead-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
          background: var(--success-color);
        }

        &.new {
          background: var(--warning-color);
        }
      }

      .lead-name {
        h6 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
          line-height: 1.2;
        }

        small {
          font-size: 12px;
          color: var(--text-muted);
        }
      }
    }

    .lead-score {
      text-align: right;

      .score-stars {
        margin-bottom: 2px;

        i {
          font-size: 12px;
          margin-right: 1px;
        }
      }

      .score-text {
        font-size: 11px;
        color: var(--text-muted);
        font-weight: 600;
      }
    }
  }

  .phase-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .current-phase {
      color: var(--primary-color);
      font-weight: 600;

      i {
        margin-right: 4px;
      }
    }

    .response-time {
      color: var(--text-muted);

      i {
        margin-right: 4px;
      }
    }

    .btn-actions {
      background: none;
      border: none;
      color: var(--text-muted);
      padding: 4px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: var(--border-color);
        color: var(--text-color);
      }
    }
  }
}

// ===== SEÇÕES GERAIS =====
.profile-section,
.ai-assistant-section,
.context-section,
.actions-section,
.notes-section,
.quick-capture-section {
  background: white;
  border-bottom: 1px solid var(--border-color);

  .section-header {
    padding: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);

    &:hover {
      background: rgba(var(--primary-color), 0.02);
    }

    .section-title {
      margin: 0;
      font-size: 13px;
      font-weight: 600;
      color: var(--text-color);
      text-transform: uppercase;
      letter-spacing: 0.5px;

      i {
        margin-right: 8px;
        color: var(--primary-color);
        font-size: 14px;
      }

      .new-badge {
        background: var(--warning-color);
        color: var(--text-color);
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 8px;
        font-weight: 700;
      }
    }

    .toggle-icon {
      color: var(--text-muted);
      font-size: 12px;
      transition: transform 0.2s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .section-content {
    padding: 16px;
    transition: all 0.3s ease;

    &.collapsed {
      max-height: 0;
      padding: 0 16px;
      overflow: hidden;
    }
  }
}

// ===== SEÇÃO PERFIL =====
.profile-section {
  .profile-info {
    .profile-main {
      margin-bottom: 12px;

      .company-name {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);

        i {
          margin-right: 6px;
          color: var(--primary-color);
          font-size: 13px;
        }
      }

      .contact-details {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: var(--text-muted);

        i {
          margin-right: 6px;
          width: 12px;
          color: var(--text-muted);
        }
      }
    }

    .profile-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 12px;

      .tag {
        font-size: 10px;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 600;

        &.hot {
          background: rgba(231, 76, 60, 0.1);
          color: #e74c3c;
        }

        &.budget {
          background: rgba(241, 196, 15, 0.1);
          color: #f1c40f;
        }

        &.segment {
          background: rgba(155, 89, 182, 0.1);
          color: #9b59b6;
        }
      }
    }

    .competitor-info {
      background: rgba(var(--danger-color), 0.05);
      border: 1px solid rgba(var(--danger-color), 0.2);
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;

      .competitor-alert {
        font-size: 11px;
        color: var(--danger-color);
        font-weight: 600;
        margin-bottom: 6px;

        i {
          margin-right: 4px;
        }
      }

      .opportunity {
        font-size: 11px;
        color: var(--success-color);
        font-weight: 600;

        i {
          margin-right: 4px;
        }
      }
    }

    .instagram-info {
      .instagram-link {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        color: #E4405F;
        text-decoration: none;

        i {
          margin-right: 4px;
        }

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// ===== SEÇÃO IA ASSISTANT =====
.ai-assistant-section {
  .section-header {
    border-bottom: none;
  }

  .phase-detection {
    background: rgba(var(--primary-color), 0.05);
    border: 1px solid rgba(var(--primary-color), 0.2);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;

    .current-phase-info {
      margin-bottom: 6px;

      .phase-label {
        font-size: 10px;
        font-weight: 700;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .phase-name {
        font-size: 12px;
        font-weight: 700;
        color: var(--primary-color);
        margin-left: 8px;
      }
    }

    .next-phase {
      .next-label {
        font-size: 10px;
        color: var(--text-muted);
      }

      .next-name {
        font-size: 11px;
        font-weight: 600;
        color: var(--success-color);
        margin-left: 4px;
      }
    }
  }

  .quick-config {
    margin-bottom: 16px;

    .mode-toggles {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 12px;

      .toggle-option {
        display: flex;
        align-items: center;
        cursor: pointer;

        input[type="checkbox"] {
          margin-right: 8px;
          accent-color: var(--primary-color);
        }

        .toggle-label {
          font-size: 12px;
          font-weight: 500;
          color: var(--text-color);

          i {
            margin-right: 4px;
            color: var(--primary-color);
          }
        }
      }
    }

    .config-label {
      font-size: 10px;
      font-weight: 700;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
      display: block;
    }

    .phase-buttons,
    .approach-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6px;
      margin-bottom: 12px;

      .phase-btn,
      .approach-btn {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 8px 6px;
        font-size: 10px;
        font-weight: 600;
        color: var(--text-muted);
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;

        i {
          display: block;
          margin-bottom: 2px;
          font-size: 12px;
        }

        &:hover {
          border-color: var(--primary-color);
          color: var(--primary-color);
        }

        &.active {
          background: var(--primary-color);
          border-color: var(--primary-color);
          color: white;
        }
      }
    }

    .tone-selector {
      .tone-select {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 12px;
        background: white;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
        }
      }
    }
  }

  .generate-section {
    margin-bottom: 16px;

    .generate-btn {
      width: 100%;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 16px;
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        margin-right: 6px;
      }

      &:hover:not(:disabled) {
        background: #5f4dd6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

    .mode-info {
      margin-top: 8px;
      text-align: center;

      small {
        font-size: 11px;
        color: var(--text-muted);

        i {
          margin-right: 4px;
        }
      }
    }
  }

  .error-message {
    background: rgba(var(--danger-color), 0.1);
    border: 1px solid rgba(var(--danger-color), 0.3);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    font-size: 12px;
    color: var(--danger-color);

    i {
      margin-right: 6px;
    }
  }

  .suggestion-result {
    margin-bottom: 16px;

    .suggestion-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .suggestion-label {
        font-size: 11px;
        font-weight: 700;
        color: var(--text-color);
      }

      .suggestion-count {
        font-size: 10px;
        background: var(--primary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: 600;
      }
    }

    .suggestion-content {
      .suggestion-text {
        margin-bottom: 12px;

        .suggestion-textarea {
          width: 100%;
          border: 1px solid var(--border-color);
          border-radius: 6px;
          padding: 12px;
          font-size: 12px;
          line-height: 1.4;
          resize: vertical;
          min-height: 80px;
          font-family: inherit;

          &:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
          }
        }
      }

      .suggestion-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;
        margin-bottom: 12px;

        .action-btn {
          border: none;
          border-radius: 6px;
          padding: 8px 12px;
          font-size: 11px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;

          i {
            margin-right: 4px;
            font-size: 10px;
          }

          &.primary {
            background: var(--success-color);
            color: white;

            &:hover {
              background: #00a085;
            }
          }

          &.secondary {
            background: var(--border-color);
            color: var(--text-color);

            &:hover {
              background: #dee2e6;
            }
          }

          &.tertiary {
            background: var(--warning-color);
            color: var(--text-color);

            &:hover {
              background: #fdb94e;
            }
          }

          &.success {
            background: var(--primary-color);
            color: white;

            &:hover {
              background: #5f4dd6;
            }
          }
        }
      }

      .suggestion-navigation {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;

        .nav-btn {
          background: none;
          border: 1px solid var(--border-color);
          border-radius: 4px;
          padding: 4px 8px;
          color: var(--text-muted);
          cursor: pointer;

          &:hover:not(:disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .nav-info {
          font-size: 11px;
          color: var(--text-muted);
          font-weight: 600;
        }
      }
    }
  }

  .quick-responses {
    margin-bottom: 16px;

    .quick-responses-label {
      font-size: 11px;
      font-weight: 700;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .quick-response-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .quick-btn {
        background: rgba(var(--primary-color), 0.1);
        border: 1px solid rgba(var(--primary-color), 0.3);
        border-radius: 16px;
        padding: 6px 12px;
        font-size: 10px;
        font-weight: 600;
        color: var(--primary-color);
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--primary-color);
          color: white;
        }
      }
    }
  }

  .ai-observation {
    background: rgba(var(--warning-color), 0.1);
    border: 1px solid rgba(var(--warning-color), 0.3);
    border-radius: 6px;
    padding: 10px;
    font-size: 11px;
    color: var(--text-color);

    i {
      margin-right: 6px;
      color: var(--warning-color);
    }
  }
}

// ===== SEÇÃO CONTEXTO =====
.context-section {
  .context-info {
    .subsection-title {
      font-size: 10px;
      font-weight: 700;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
    }

    .triggers-section {
      margin-bottom: 16px;

      .trigger-list {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .trigger-item {
          font-size: 11px;
          color: var(--text-color);
        }
      }
    }

    .sentiment-section {
      margin-bottom: 16px;

      .sentiment-flow {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 11px;

        .sentiment-from {
          color: var(--danger-color);
          font-weight: 600;
        }

        .sentiment-to {
          color: var(--success-color);
          font-weight: 600;
        }

        i {
          color: var(--text-muted);
        }
      }
    }

    .qualification-section {
      margin-bottom: 16px;

      .bant-indicators {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4px;

        .bant-item {
          font-size: 10px;
          padding: 4px 6px;
          border-radius: 4px;
          text-align: center;

          &.qualified {
            background: rgba(var(--success-color), 0.1);
            color: var(--success-color);
            font-weight: 600;
          }
        }
      }
    }

    .next-steps-section {
      .steps-list {
        .step-item {
          font-size: 11px;
          color: var(--text-color);
          margin-bottom: 4px;
          padding-left: 8px;
        }
      }
    }
  }
}

// ===== SEÇÃO AÇÕES RÁPIDAS =====
.actions-section {
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .action-button {
      display: flex;
      align-items: center;
      gap: 12px;
      background: white;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        font-size: 16px;
        width: 20px;
        text-align: center;
      }

      .action-info {
        flex: 1;
        text-align: left;

        .action-title {
          display: block;
          font-size: 12px;
          font-weight: 600;
          color: var(--text-color);
          margin-bottom: 2px;
        }

        .action-subtitle {
          font-size: 10px;
          color: var(--text-muted);
        }
      }

      &:hover {
        border-color: var(--primary-color);
        box-shadow: 0 2px 8px rgba(var(--primary-color), 0.1);
      }

      &.schedule {
        i { color: var(--primary-color); }
      }

      &.proposal {
        i { color: var(--warning-color); }
      }

      &.call {
        i { color: var(--success-color); }
      }

      &.followup {
        i { color: var(--danger-color); }
      }
    }
  }
}

// ===== SEÇÃO NOTAS =====
.notes-section {
  .notes-input {
    margin-bottom: 12px;

    .notes-textarea {
      width: 100%;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 12px;
      font-size: 12px;
      line-height: 1.4;
      resize: vertical;
      min-height: 80px;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
      }
    }
  }

  .notes-actions {
    display: flex;
    gap: 6px;
    margin-bottom: 16px;

    .notes-btn {
      flex: 1;
      border: none;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 11px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        margin-right: 4px;
        font-size: 10px;
      }

      &.save {
        background: var(--success-color);
        color: white;

        &:hover {
          background: #00a085;
        }
      }

      &.copy {
        background: var(--border-color);
        color: var(--text-color);

        &:hover {
          background: #dee2e6;
        }
      }

      &.ai {
        background: var(--primary-color);
        color: white;

        &:hover {
          background: #5f4dd6;
        }
      }
    }
  }

  .notes-history {
    .history-title {
      font-size: 10px;
      font-weight: 700;
      color: var(--text-muted);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
    }

    .history-list {
      .history-item {
        display: flex;
        gap: 8px;
        margin-bottom: 4px;

        .history-time {
          font-size: 10px;
          color: var(--text-muted);
          font-weight: 600;
          min-width: 40px;
        }

        .history-text {
          font-size: 10px;
          color: var(--text-color);
        }
      }
    }
  }
}

// ===== SEÇÃO CAPTURA RÁPIDA =====
.quick-capture-section {
  .capture-form {
    .form-group {
      margin-bottom: 12px;

      .form-label {
        display: block;
        font-size: 11px;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 4px;
      }

      .form-input,
      .form-textarea {
        width: 100%;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 8px 10px;
        font-size: 12px;
        font-family: inherit;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
        }

        &[readonly] {
          background: var(--bg-light);
          color: var(--text-muted);
        }
      }

      .form-textarea {
        resize: vertical;
        min-height: 60px;
      }
    }

    .form-actions {
      .form-btn {
        width: 100%;
        border: none;
        border-radius: 6px;
        padding: 10px 16px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        i {
          margin-right: 6px;
        }

        &.primary {
          background: var(--success-color);
          color: white;

          &:hover {
            background: #00a085;
          }
        }
      }
    }
  }
}

// ===== FOOTER STATUS =====
.status-footer {
  background: white;
  border-top: 1px solid var(--border-color);
  padding: 12px 16px;

  .status-indicators {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 10px;

    .status-item {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--text-muted);

      i {
        font-size: 8px;
      }

      &.online {
        color: var(--success-color);

        i {
          color: var(--success-color);
        }
      }
    }
  }
}

// ===== ANIMAÇÕES =====
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.fa-spin {
  animation: spin 1s linear infinite;
}

// ===== RESPONSIVIDADE =====
@media (max-width: 400px) {
  .whatsapp-assistant-panel {
    width: 100vw;
  }

  .phase-buttons,
  .approach-buttons {
    grid-template-columns: 1fr !important;
  }

  .bant-indicators {
    grid-template-columns: 1fr !important;
  }

  .suggestion-actions {
    grid-template-columns: 1fr !important;

    .action-btn {
      margin-bottom: 4px;
    }
  }
}

// ===== UTILITÁRIOS =====
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

.user-select-none {
  user-select: none;
}

