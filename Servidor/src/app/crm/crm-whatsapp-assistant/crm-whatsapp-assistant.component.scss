// Estilos específicos do CrmWhatsappAssistant

.avatar-sm {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar-title {
    font-size: 1rem;
    font-weight: 600;
    color: white;
  }
}

.sugestao-container,
.sugestoes-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;

  textarea {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5;
    resize: vertical;
    min-height: 120px;
  }

  // Lista vertical de sugestões
  .sugestoes-lista {
    .sugestao-item {
      .card {
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
        
        &:hover {
          border-color: #6c5ce7;
          box-shadow: 0 2px 8px rgba(108, 92, 231, 0.15);
        }
        
        .card-header {
          background-color: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          
          h6 {
            color: #495057;
            font-size: 0.95rem;
            
            i {
              color: #6c5ce7;
              margin-right: 0.5rem;
            }
          }
        }
        
        .card-body {
          padding: 1rem;
          
          textarea {
            border: 1px solid #e2e6ea;
            background-color: white;
            
            &:focus {
              border-color: #6c5ce7;
              box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.15);
            }
          }
          
          .btn {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            
            &.btn-success {
              background-color: #28a745;
              border-color: #28a745;
              
              &:hover {
                background-color: #218838;
                border-color: #1e7e34;
              }
            }
          }
        }
      }
    }
  }
}

// Estados dos badges SPIN
.badge {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  &.badge-primary {
    background-color: #6c5ce7;
    border: 2px solid #6c5ce7;
  }

  &.badge-light {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #6c757d;

    &:hover {
      background-color: #e9ecef;
      color: #495057;
    }
  }
}

// Animação de loading
.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Transição suave para loading
.container-fluid {
  min-height: 400px;
  position: relative;
  
  > div {
    animation: fadeIn 0.3s ease-in-out;
  }
}

// Loading overlay com fade suave
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

// Estilo para área de contexto
.bg-light {
  background-color: #f8f9fa !important;

  p {
    font-style: italic;
    line-height: 1.6;
  }
}

// Botões de ação
.gap-2 {
  gap: 0.5rem;
}

// Responsividade para telas menores
@media (max-width: 768px) {
  .btn-group {
    .btn {
      display: block;
      width: 100%;
      margin-bottom: 0.25rem;
    }
  }

  .d-flex.justify-content-center {
    flex-direction: column;

    .btn {
      margin-bottom: 0.5rem;
    }
  }
}

// Estados visuais para feedback
.card {
  transition: box-shadow 0.15s ease-in-out;

  &:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
}

// Destacar botão principal
.btn-primary.btn-lg {
  padding: 0.75rem 2rem;
  font-weight: 600;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(108, 92, 231, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 92, 231, 0.4);
  }

  &:disabled {
    transform: none;
    box-shadow: none;
  }
}

// Estilo para notas privadas e aumento de altura
textarea {
  &::placeholder {
    color: #6c757d;
    font-style: italic;
  }
  
  // Aumentar altura mínima para todas as textareas
  min-height: 120px !important;
  
  // Permitir redimensionamento vertical
  resize: vertical;
}

// Ícones com espaçamento
i {
  margin-right: 0.25rem;

  &.text-primary {
    color: #6c5ce7 !important;
  }
}

// Card headers com ícones
.card-header {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;

  .card-title {
    color: #495057;
    font-weight: 600;

    i {
      color: #6c5ce7;
      margin-right: 0.5rem;
    }
  }
}

// Alertas customizados
.alert {
  border: none;
  border-radius: 0.5rem;

  &.alert-danger {
    background-color: #f8d7da;
    color: #721c24;

    i {
      margin-right: 0.5rem;
    }
  }
}

// Breadcrumb customizado
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin: 0;

  .breadcrumb-item {
    &.active {
      color: #6c5ce7;
    }

    a {
      color: #6c757d;
      text-decoration: none;

      &:hover {
        color: #6c5ce7;
      }
    }
  }
}

// Animação para feedback de cópia
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Seção de configuração
.config-section {
  border-left: 4px solid #6c5ce7;
  background: linear-gradient(to right, #f8f9fa, #ffffff);

  .form-label {
    color: #495057;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Botões SPIN melhorados
.btn-group {
  .btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &.badge-primary {
      background: linear-gradient(135deg, #6c5ce7, #5f4dd6);
      border-color: #6c5ce7;
      color: white;
      box-shadow: 0 2px 4px rgba(108, 92, 231, 0.3);

      i {
        color: white !important;
      }
    }

    &.badge-light {
      background: #ffffff;
      border: 2px solid #e9ecef;
      color: #6c757d;

      &:hover {
        background: #f8f9fa;
        border-color: #6c5ce7;
        color: #6c5ce7;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }
    }

    i {
      font-size: 0.9rem;
      margin-right: 0.4rem;
    }
  }
}

// Select melhorado
.form-select {
  border: 2px solid #e9ecef;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;

  &:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
  }
}

// Botão principal com sombra
.btn-primary.btn-lg.shadow {
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 92, 231, 0.5);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
  }
}

// Estilos para novos leads
.border-warning {
  border-color: #ffc107 !important;
}

.border-success {
  border-color: #28a745 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
  
  .avatar-title {
    color: #212529 !important;
  }
}

// Card de captura rápida
.collapse {
  transition: all 0.35s ease;
}

.card.bg-light {
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6;
  
  .card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
  }
}

// Badge NOVO
.badge-warning {
  background-color: #ffc107;
  color: #212529;
  font-weight: 600;
  animation: pulse-warning 2s infinite;
}

// Badge Concorrente
.badge-danger {
  animation: pulse-danger 2s infinite;
  font-weight: 600;
  
  i {
    margin-right: 0.25rem;
  }
}

@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

@keyframes pulse-danger {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

// Inputs da captura rápida
.form-control {
  &:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
  }
}

// Texto de ajuda
.text-warning {
  color: #ffc107 !important;
  
  &.d-block {
    font-size: 0.875rem;
    font-weight: normal;
  }
}

// Card de inteligência competitiva
.card.border-danger {
  border-width: 2px;
  
  .card-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    
    .card-title {
      color: white !important;
      
      i {
        color: white !important;
      }
    }
  }
}

// Listas de pontos competitivos
.list-unstyled {
  li {
    padding: 0.25rem 0;
    
    .fe-minus {
      margin-right: 0.5rem;
    }
    
    .fe-check {
      margin-right: 0.5rem;
    }
  }
}

// Alert de sugestão de abordagem
.alert-warning {
  border-left: 4px solid #ffc107;
  background-color: #fff3cd;
  
  small {
    line-height: 1.4;
  }
}