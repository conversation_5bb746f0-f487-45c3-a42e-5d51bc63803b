import { Injectable } from '@angular/core';
import { NotificationService } from './notification.service';

export interface LogDiagnostico {
  timestamp: Date;
  telefone: string;
  nome: string;
  tipo: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  categoria: 'PROCESSAMENTO' | 'VALIDACAO' | 'ENVIO' | 'SISTEMA';
  mensagem: string;
  detalhes?: any;
}

@Injectable({
  providedIn: 'root'
})
export class DiagnosticoWhatsappService {
  private logs: LogDiagnostico[] = [];
  private maxLogs = 100; // Manter apenas os últimos 100 logs

  constructor(private notificationService: NotificationService) {}

  /**
   * Adiciona um log de diagnóstico
   */
  adicionarLog(log: Omit<LogDiagnostico, 'timestamp'>): void {
    const novoLog: LogDiagnostico = {
      ...log,
      timestamp: new Date()
    };

    this.logs.unshift(novoLog);

    // Manter apenas os logs mais recentes
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Log no console para debug técnico
    console.log(`[DiagnosticoWhatsapp] ${log.categoria} - ${log.tipo}: ${log.mensagem}`, log.detalhes);
    console.log(`[DiagnosticoWhatsapp] Total de logs armazenados: ${this.logs.length}`);
  }

  /**
   * Logs específicos para processamento de mensagens
   */
  logProcessamentoMensagem(telefone: string, nome: string, mensagem: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'INFO',
      categoria: 'PROCESSAMENTO',
      mensagem,
      detalhes
    });
  }

  logValidacaoFalhou(telefone: string, nome: string, motivo: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'WARNING',
      categoria: 'VALIDACAO',
      mensagem: `Validação falhou: ${motivo}`,
      detalhes
    });
  }

  logEnvioSucesso(telefone: string, nome: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'SUCCESS',
      categoria: 'ENVIO',
      mensagem: 'Mensagem de saudação enviada com sucesso',
      detalhes
    });
  }

  logEnvioFalhou(telefone: string, nome: string, motivo: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'ERROR',
      categoria: 'ENVIO',
      mensagem: `Envio falhou: ${motivo}`,
      detalhes
    });
  }

  /**
   * Obtém logs filtrados por telefone
   */
  obterLogsPorTelefone(telefone: string): LogDiagnostico[] {
    console.log(`[DiagnosticoWhatsapp] Buscando logs para telefone: "${telefone}"`);
    console.log(`[DiagnosticoWhatsapp] Telefones nos logs:`, this.logs.map(log => log.telefone));

    const logsEncontrados = this.logs.filter(log => log.telefone === telefone);
    console.log(`[DiagnosticoWhatsapp] Logs encontrados: ${logsEncontrados.length}`);

    return logsEncontrados;
  }

  /**
   * Obtém todos os logs
   */
  obterTodosLogs(): LogDiagnostico[] {
    return [...this.logs];
  }

  /**
   * Obtém logs recentes (últimos 10)
   */
  obterLogsRecentes(): LogDiagnostico[] {
    return this.logs.slice(0, 10);
  }

  /**
   * Limpa todos os logs
   */
  limparLogs(): void {
    this.logs = [];
  }

  /**
   * Exibe notificação para o usuário sobre status do processamento
   */
  notificarUsuario(tipo: 'success' | 'error' | 'info', mensagem: string): void {
    switch (tipo) {
      case 'success':
        this.notificationService.showSuccess(mensagem);
        break;
      case 'error':
        this.notificationService.showError(mensagem);
        break;
      case 'info':
        // Para mensagens informativas, usar o método genérico do Kendo
        this.notificationService['notificationService'].show({
          content: mensagem,
          cssClass: 'info',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'info', icon: true },
          hideAfter: 4000
        });
        break;
    }
  }

  /**
   * Gera relatório de diagnóstico formatado
   */
  gerarRelatorio(telefone?: string): string {
    console.log(`[DiagnosticoWhatsapp] Gerando relatório para telefone: ${telefone}`);
    console.log(`[DiagnosticoWhatsapp] Total de logs no sistema: ${this.logs.length}`);

    const logsParaRelatorio = telefone ? this.obterLogsPorTelefone(telefone) : this.obterLogsRecentes();

    console.log(`[DiagnosticoWhatsapp] Logs para relatório: ${logsParaRelatorio.length}`);

    if (logsParaRelatorio.length === 0) {
      const mensagem = telefone
        ? `Nenhum log encontrado para o telefone ${telefone}.\n\nTotal de logs no sistema: ${this.logs.length}`
        : `Nenhum log de diagnóstico encontrado no sistema.`;
      return mensagem;
    }

    let relatorio = `📊 RELATÓRIO DE DIAGNÓSTICO WHATSAPP\n`;
    relatorio += `📅 Gerado em: ${new Date().toLocaleString('pt-BR')}\n`;
    
    if (telefone) {
      relatorio += `📞 Telefone: ${telefone}\n`;
    }
    
    relatorio += `📝 Total de logs: ${logsParaRelatorio.length}\n\n`;

    // Agrupar por categoria
    const categorias = ['PROCESSAMENTO', 'VALIDACAO', 'ENVIO', 'SISTEMA'];
    
    categorias.forEach(categoria => {
      const logsDaCategoria = logsParaRelatorio.filter(log => log.categoria === categoria);
      
      if (logsDaCategoria.length > 0) {
        relatorio += `📋 ${categoria}:\n`;
        
        logsDaCategoria.slice(0, 5).forEach(log => {
          const emoji = this.obterEmojiPorTipo(log.tipo);
          relatorio += `${emoji} [${log.timestamp.toLocaleTimeString('pt-BR')}] ${log.nome || 'N/A'}: ${log.mensagem}\n`;
        });
        
        relatorio += '\n';
      }
    });

    return relatorio;
  }

  private obterEmojiPorTipo(tipo: string): string {
    const emojis = {
      'INFO': 'ℹ️',
      'WARNING': '⚠️',
      'ERROR': '❌',
      'SUCCESS': '✅'
    };
    return emojis[tipo] || 'ℹ️';
  }

  /**
   * Obtém estatísticas dos logs
   */
  obterEstatisticas(telefone?: string): any {
    const logs = telefone ? this.obterLogsPorTelefone(telefone) : this.obterTodosLogs();
    
    const stats = {
      total: logs.length,
      porTipo: {
        INFO: 0,
        WARNING: 0,
        ERROR: 0,
        SUCCESS: 0
      },
      porCategoria: {
        PROCESSAMENTO: 0,
        VALIDACAO: 0,
        ENVIO: 0,
        SISTEMA: 0
      },
      ultimoLog: logs[0]?.timestamp || null
    };

    logs.forEach(log => {
      stats.porTipo[log.tipo]++;
      stats.porCategoria[log.categoria]++;
    });

    return stats;
  }
}
