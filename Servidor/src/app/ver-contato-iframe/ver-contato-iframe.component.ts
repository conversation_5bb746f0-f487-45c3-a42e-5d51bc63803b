import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from '@angular/core';
import {SelosComponent} from "../componentes/selo/selo.component";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";
import {ContatosService} from "../services/contatos.service";
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";
import {AutorizacaoService} from "../services/autorizacao.service";

import {ConstantsService} from "../fidelidade/ConstantsService";
import {ArmazenamentoService} from "../services/armazenamento.service";
import {BotsService} from "../services/bots.service";
import {Subscription} from "rxjs";
import {DiagnosticoWhatsappService} from "../services/diagnostico-whatsapp.service";

@Component({
  selector: 'app-ver-contato-iframe',
  templateUrl: './ver-contato-iframe.component.html',
  styleUrls: ['./ver-contato-iframe.component.scss']
})
export class VerContatoIframeComponent implements OnInit, OnDestroy {
  acoes: any = [];
  cartoes: any = [];
  filtro: any = {cartao: null};
  contato: any = null;
  @ViewChild('selosCartao', { static: true } ) selosCartao: SelosComponent;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  abraDialogo: boolean;
  telefone: any;
  nomeContato: any;
  widget = false;
  usuario: any;
  empresa: any;
  pedidosEmAberto: any[];
  pedidosAnteriores: any[];
  carregou: boolean;
  novoPedido: any = {}
  msg: any = '';
  dev = false;
  assumirComandoBot: any;
  atendente = false;
  desativarParaSempre = false;
  statusMia = false;
  contatoPorDados: any = {};
  assinante: Subscription;

  constructor(  private  contatosService: ContatosService, private router: Router,
               private activatedRoute: ActivatedRoute, private _location: Location,
               private autorizacaoService: AutorizacaoService, private constantsService: ConstantsService,
               private armazenamentoService: ArmazenamentoService, private botsService: BotsService,
               private diagnosticoWhatsappService: DiagnosticoWhatsappService) {
    this.dev = (window.location.href.indexOf('localhost') !== -1);
  }

  inIframe () {
    try {
      return window.self !== window.top;
    } catch (e) {
      return true;
    }
  }

  calculeSeBotEstahAtivo(telefone: string) {
    if( !this.usuario || !telefone ) {
      return;
    }

    this.botsService.estahComAtedente(this.empresa, telefone).then( (respSessao: any) => {
      this.atendente = respSessao.data.atendente;
      this.desativarParaSempre = respSessao.data.desativarParaSempre;

      const configuracoesMia = respSessao.data.mia;

      if( configuracoesMia ) {
        this.statusMia = (configuracoesMia.status !== 'DESATIVADA');
      }
    });
  }

  ngOnInit() {
    this.msg = this.activatedRoute.snapshot.queryParams.msg;
    this.novoPedido = { guid: this.activatedRoute.snapshot.queryParams.codigo};

    let usuarioLogado = this.autorizacaoService.getUser();

    if( usuarioLogado != null )
      this.usuario = usuarioLogado;
    else {
      this.usuario = {};
    }

    this.autorizacaoService.usuarioLogado$.subscribe( (usuario) => {
      if( !usuario ) return;

      this.usuario = usuario;
    });

    const gerarLinkPagamento  = this.activatedRoute.snapshot.queryParams.gmlkpg;

    //gerar msg link pagamento
    if(gerarLinkPagamento)
      window['root'].envieMensagemLinkPagamento(gerarLinkPagamento);



    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return; }

      this.empresa = empresa;

      this.assinante = this.botsService.eventoEstadoMia$.subscribe( (dados: any) => {
        if( this.contato ) {
          this.calculeSeBotEstahAtivo(this.contato.codigoPais + this.contato.telefone);
        }
      });

      this.activatedRoute.queryParams.subscribe(queryParams => {
        this.nomeContato = this.activatedRoute.snapshot.queryParams.nome;

        if( this.nomeContato === 'undefined' || this.nomeContato === undefined ) {
          this.nomeContato = '';
        }

        this.widget = this.inIframe();

        const { contato } = window.history.state;

        if (contato) {
          this.setContato(contato);
        }
        else {
          this.telefone = decodeURIComponent(this.activatedRoute.snapshot.params['zap']);
          this.contatoPorDados = this.gereNovoContato(this.nomeContato, this.telefone)
          this.contatosService.obtenhaContato(this.telefone.toString()).then( (objContato: any) => {
            this.setContato(objContato);
          });
        }

        this.calculeSeBotEstahAtivo(this.activatedRoute.snapshot.params['zap']);
      });
    });
  }

  salveContato(contato: any) {
    this.contato = contato;
  }

  gereNovoContato(nomeContato: string, telefoneCompleto: string) {

    const { codigoPais, telefone } = this.contatosService.extraiCodigoPaisETelefone(telefoneCompleto)

    return { nome: nomeContato,
      codigoPais: codigoPais,
      telefone: telefone
    }
  }


  setContato(contato: any) {
    this.contato = contato;

    let estadoAnterior: any = JSON.parse(this.armazenamentoService.carregue('url_' + this.telefone))  ;

    if(estadoAnterior) {
      this.router.navigateByUrl(estadoAnterior.url, {state: estadoAnterior.state});
      return;
    }

    this.carregou = true;
    if( !contato ) {
      return;
    }

    this.cartoes = contato.cartoes;
    this.cartoes.forEach( cartao => cartao.descricao = cartao.plano.nome)
  }

  voltar() {
    this._location.back();
  }

  recarregue() {
    window.location.reload();
  }

  imprimaPedido(pedido: any) {
    window.open('/imprimir/pedido/' + pedido.guid);
  }

  digitouMensagemBot($event: any) {
    const mensagem = $event.target.value;

    window['root'].novasMensagens([mensagem]);

    $event.target.value = '';
  }

  alterouStatusBot() {

  }

  executarDiagnostico() {
    if (!this.contato) {
      alert('Nenhum contato carregado para diagnóstico.');
      return;
    }

    const diagnostico = this.coletarInformacoesDiagnostico();
    this.exibirResultadoDiagnostico(diagnostico);
  }

  private coletarInformacoesDiagnostico(): any {
    const diagnostico: any = {
      timestamp: new Date().toLocaleString('pt-BR'),
      contato: {},
      whatsapp: {},
      mia: {},
      sistema: {},
      logsWhatsapp: {}
    };

    // Informações do contato
    if (this.contato) {
      diagnostico.contato = {
        id: this.contato.id || 'Não definido',
        nome: this.contato.nome || 'Não informado',
        telefone: this.contato.telefone || 'Não informado',
        codigoPais: this.contato.codigoPais || 'Não informado',
        telefoneCompleto: (this.contato.codigoPais || '') + (this.contato.telefone || ''),
        status: this.contato.status || 'Não definido',
        email: this.contato.email || 'Não informado',
        bloqueado: this.contato.bloqueado ? 'Sim' : 'Não',
        dataAtivacao: this.contato.dataAtivacao || 'Não definido',
        ultimaVisita: this.contato.ultimaVisita || 'Não definido',
        quantidadeCartoes: this.contato.cartoes ? this.contato.cartoes.length : 0,
        tags: this.contato.tags ? this.contato.tags.map((tag: any) => tag.nome).join(', ') : 'Nenhuma'
      };
    }

    // Informações do WhatsApp/Mia
    diagnostico.mia = {
      statusMia: this.statusMia ? 'Ativa' : 'Inativa',
      atendente: this.atendente ? 'Sim' : 'Não',
      desativarParaSempre: this.desativarParaSempre ? 'Sim' : 'Não'
    };

    // Informações do sistema
    diagnostico.sistema = {
      carregou: this.carregou ? 'Sim' : 'Não',
      widget: this.widget ? 'Sim' : 'Não',
      dev: this.dev ? 'Sim' : 'Não',
      empresaId: this.empresa?.id || 'Não definido',
      empresaNome: this.empresa?.nome || 'Não definido',
      usuarioId: this.usuario?.id || 'Não definido',
      usuarioNome: this.usuario?.nome || 'Não definido'
    };

    // Informações do WhatsApp (se disponível)
    diagnostico.whatsapp = {
      numeroWhatsapp: (window as any)['numeroWhatsapp'] || 'Não disponível',
      carregouWhatsapp: (window as any)['carregouWhatsapp'] || false,
      inIframe: this.inIframe() ? 'Sim' : 'Não'
    };

    // Logs específicos do WhatsApp para este contato
    if (this.contato?.telefone) {
      const telefoneCompleto = (this.contato.codigoPais || '') + this.contato.telefone;
      const logsContato = this.diagnosticoWhatsappService.obterLogsPorTelefone(telefoneCompleto);
      const estatisticas = this.diagnosticoWhatsappService.obterEstatisticas(telefoneCompleto);

      diagnostico.logsWhatsapp = {
        totalLogs: logsContato.length,
        ultimoLog: logsContato[0]?.timestamp || 'Nenhum',
        estatisticas: estatisticas,
        logsRecentes: logsContato.slice(0, 5).map(log => ({
          timestamp: log.timestamp.toLocaleTimeString('pt-BR'),
          tipo: log.tipo,
          categoria: log.categoria,
          mensagem: log.mensagem
        }))
      };
    } else {
      diagnostico.logsWhatsapp = {
        totalLogs: 0,
        ultimoLog: 'N/A - Contato sem telefone',
        estatisticas: null,
        logsRecentes: []
      };
    }

    return diagnostico;
  }

  private exibirResultadoDiagnostico(diagnostico: any) {
    let mensagem = `🔍 DIAGNÓSTICO DO CONTATO\n`;
    mensagem += `⏰ Executado em: ${diagnostico.timestamp}\n\n`;

    mensagem += `👤 CONTATO:\n`;
    mensagem += `• ID: ${diagnostico.contato.id}\n`;
    mensagem += `• Nome: ${diagnostico.contato.nome}\n`;
    mensagem += `• Telefone: ${diagnostico.contato.telefoneCompleto}\n`;
    mensagem += `• Status: ${diagnostico.contato.status}\n`;
    mensagem += `• Bloqueado: ${diagnostico.contato.bloqueado}\n`;
    mensagem += `• Email: ${diagnostico.contato.email}\n`;
    mensagem += `• Cartões: ${diagnostico.contato.quantidadeCartoes}\n`;
    mensagem += `• Tags: ${diagnostico.contato.tags}\n\n`;

    mensagem += `🤖 MIA (CHATBOT):\n`;
    mensagem += `• Status: ${diagnostico.mia.statusMia}\n`;
    mensagem += `• Atendente ativo: ${diagnostico.mia.atendente}\n`;
    mensagem += `• Desativada permanentemente: ${diagnostico.mia.desativarParaSempre}\n\n`;

    mensagem += `📱 WHATSAPP:\n`;
    mensagem += `• Número WhatsApp: ${diagnostico.whatsapp.numeroWhatsapp}\n`;
    mensagem += `• WhatsApp carregado: ${diagnostico.whatsapp.carregouWhatsapp}\n`;
    mensagem += `• Em iframe: ${diagnostico.whatsapp.inIframe}\n\n`;

    mensagem += `⚙️ SISTEMA:\n`;
    mensagem += `• Componente carregado: ${diagnostico.sistema.carregou}\n`;
    mensagem += `• Modo widget: ${diagnostico.sistema.widget}\n`;
    mensagem += `• Modo desenvolvimento: ${diagnostico.sistema.dev}\n`;
    mensagem += `• Empresa: ${diagnostico.sistema.empresaNome} (ID: ${diagnostico.sistema.empresaId})\n`;
    mensagem += `• Usuário: ${diagnostico.sistema.usuarioNome} (ID: ${diagnostico.sistema.usuarioId})\n\n`;

    mensagem += `📋 LOGS WHATSAPP:\n`;
    mensagem += `• Total de logs: ${diagnostico.logsWhatsapp.totalLogs}\n`;
    mensagem += `• Último log: ${diagnostico.logsWhatsapp.ultimoLog}\n`;

    if (diagnostico.logsWhatsapp.estatisticas) {
      const stats = diagnostico.logsWhatsapp.estatisticas;
      mensagem += `• Sucessos: ${stats.porTipo.SUCCESS || 0}\n`;
      mensagem += `• Avisos: ${stats.porTipo.WARNING || 0}\n`;
      mensagem += `• Erros: ${stats.porTipo.ERROR || 0}\n`;
    }

    if (diagnostico.logsWhatsapp.logsRecentes.length > 0) {
      mensagem += `\n📝 LOGS RECENTES:\n`;
      diagnostico.logsWhatsapp.logsRecentes.forEach((log: any) => {
        const emoji = this.obterEmojiPorTipo(log.tipo);
        mensagem += `${emoji} [${log.timestamp}] ${log.mensagem}\n`;
      });
    }

    // Exibir em alert (pode ser substituído por um modal mais elegante no futuro)
    alert(mensagem);

    // Log no console para debug técnico
    console.log('🔍 Diagnóstico completo:', diagnostico);
  }

  private obterEmojiPorTipo(tipo: string): string {
    const emojis: any = {
      'INFO': 'ℹ️',
      'WARNING': '⚠️',
      'ERROR': '❌',
      'SUCCESS': '✅'
    };
    return emojis[tipo] || 'ℹ️';
  }

  /**
   * Método para exibir relatório detalhado dos logs do WhatsApp
   */
  exibirRelatorioWhatsapp(): void {
    console.log('[VerContatoIframe] Iniciando relatório WhatsApp');
    console.log('[VerContatoIframe] Serviço disponível:', !!this.diagnosticoWhatsappService);

    if (!this.contato?.telefone) {
      alert('Nenhum contato com telefone carregado para gerar relatório.');
      return;
    }

    const telefoneCompleto = (this.contato.codigoPais || '') + this.contato.telefone;
    console.log('[VerContatoIframe] Telefone completo:', telefoneCompleto);

    const relatorio = this.diagnosticoWhatsappService.gerarRelatorio(telefoneCompleto);
    console.log('[VerContatoIframe] Relatório gerado:', relatorio);

    alert(relatorio);
  }

  ngOnDestroy(): void {
    this.assinante.unsubscribe();
  }
}
